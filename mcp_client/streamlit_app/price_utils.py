"""
Price utilities for SNCF train journey pricing.

This module handles price extraction and formatting for different tariff types.
"""
import re
from typing import Dict, <PERSON><PERSON>


def clean_unicode(text: str) -> str:
    """Clean Unicode escape sequences."""
    return text.replace("\\u20ac", "€").replace("\\u00e8", "è").replace("\\u00e9", "é").replace("\\u00e0", "à")


def format_price_range(price_str: str) -> str:
    """Extract and format price range from string."""
    if not price_str:
        return "N/A"
    
    cleaned = clean_unicode(str(price_str))
    
    # Handle the new format: "18.0€ - 18.0€" or "16.0€ - 95.0€"
    match = re.search(r'(\d+\.?\d*)€?\s*-\s*(\d+\.?\d*)€?', cleaned)
    if match:
        min_p, max_p = int(float(match.group(1))), int(float(match.group(2)))
        return f"{min_p}€ - {max_p}€"
    
    # Handle single price format
    match = re.search(r'(\d+\.?\d*)€?', cleaned)
    if match:
        price = int(float(match.group(1)))
        return f"{price}€"
    
    return cleaned


def extract_price_info(price_data, selected_tariff: str = "Tarif Normal") -> str:
    """
    Extract price information from various formats for a specific tariff.
    Falls back to "Tarif Normal" if the selected tariff is not available.

    Args:
        price_data: Price data from transport_info
        selected_tariff: The selected tariff type

    Returns:
        Formatted price string for the selected tariff, or "Tarif Normal" as fallback, or "N/A" if no prices available
    """
    if not price_data:
        return "N/A"

    # Handle dictionary format (most common)
    if isinstance(price_data, dict):
        # Try selected tariff first
        for key, value in price_data.items():
            if key == selected_tariff or clean_unicode(key) == selected_tariff:
                result = format_price_range(value)
                if result != "N/A":
                    return result
        
        # Fallback to "Tarif Normal" if different from selected
        if selected_tariff != "Tarif Normal":
            for key, value in price_data.items():
                if key == "Tarif Normal" or clean_unicode(key) == "Tarif Normal":
                    result = format_price_range(value)
                    if result != "N/A":
                        return result
        
        # Legacy format support
        if "min_price" in price_data and "max_price" in price_data:
            min_p = int(float(price_data["min_price"]))
            max_p = int(float(price_data["max_price"]))
            return f"{min_p}€ - {max_p}€"

    # Handle string format
    elif isinstance(price_data, str):
        # Parse comma-separated tariff:price format
        tariffs = {}
        for part in price_data.split(", "):
            if ":" in part:
                name, price = part.split(":", 1)
                tariffs[name.strip()] = price.strip()

        # Try selected tariff first
        if selected_tariff in tariffs:
            return format_price_range(tariffs[selected_tariff])
        
        # Try with Unicode cleaning
        for key, value in tariffs.items():
            if clean_unicode(key) == selected_tariff:
                return format_price_range(value)
        
        # Fallback to "Tarif Normal"
        if selected_tariff != "Tarif Normal":
            if "Tarif Normal" in tariffs:
                return format_price_range(tariffs["Tarif Normal"])
            for key, value in tariffs.items():
                if clean_unicode(key) == "Tarif Normal":
                    return format_price_range(value)

    return "N/A"
