#!/usr/bin/env python3
"""
Test script to debug HTML generation issues.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from streamlit_app.utils import extract_all_journey_data_from_tool_calls, generate_multiple_html_templates

# Test data that simulates what we might get from a tool call
test_tool_calls = [
    {
        "tool_name": "get_journeys",
        "result": {
            "journeys": [
                {
                    "departure": "20241220T140000",
                    "arrival": "20241220T170000", 
                    "duration": 10800,  # 3 hours in seconds
                    "nb_transfers": 0,
                    "sections": [
                        {
                            "from": "Paris Gare de Lyon",
                            "to": "Rennes",
                            "departure_time": "20241220T140000",
                            "arrival_time": "20241220T170000"
                        }
                    ],
                    "journey_prices": {
                        "normal": "45.00 - 85.00"
                    }
                }
            ],
            "from_station": "Paris Gare de Lyon",
            "to_station": "Rennes"
        }
    }
]

def test_html_generation():
    """Test the HTML generation process."""
    print("Testing HTML generation...")
    
    # Test data extraction
    journey_data_list = extract_all_journey_data_from_tool_calls(test_tool_calls)
    print(f"Extracted {len(journey_data_list)} journey data items")
    
    if journey_data_list:
        print("First journey data keys:", list(journey_data_list[0].keys()))
    
    # Test HTML generation
    html_content = generate_multiple_html_templates(test_tool_calls, "Tarif Normal")
    print(f"Generated HTML length: {len(html_content)}")
    
    if html_content:
        print("HTML generation successful!")
        print("First 200 characters of HTML:")
        print(html_content[:200])
    else:
        print("HTML generation failed!")
    
    return html_content

if __name__ == "__main__":
    test_html_generation()
